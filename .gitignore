# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*.pyo
*.pyd

# Virtual environment
.venv/
env/
venv/

# Editor / IDE settings
.vscode/
.idea/
*.swp

# Environment variables
.env
.env.*

# SQLite database
instance/
*.db
*.sqlite3

# Migrations (optional if auto-generated or managed externally)
migrations/__pycache__/
migrations/versions/*.pyc
migrations/versions/*.pyo

# Flask session or temp files
*.log
*.tmp

# OS files
.DS_Store
Thumbs.db

# Pytest cache
.pytest_cache/

# Coverage reports
htmlcov/
.coverage
.tox/

# Jupyter Notebooks (if used)
.ipynb_checkpoints/