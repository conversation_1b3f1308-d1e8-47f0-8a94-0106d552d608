from flask import Flask
from flask_cors import CORS
from app.extensions import db, jwt
import os

def create_app():
    app = Flask(__name__)
    CORS(app)

    app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv("DATABASE_URL", "sqlite:///./instance/app.db")
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['JWT_SECRET_KEY'] = os.getenv("JWT_SECRET_KEY", "super-secret")

    db.init_app(app)
    jwt.init_app(app)

    from app.routes.auth import auth_bp
    from app.routes.issues import issues_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(issues_bp, url_prefix='/issues')

    with app.app_context():
        db.create_all()

    return app