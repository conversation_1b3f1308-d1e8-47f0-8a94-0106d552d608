from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.issue import Issue
from app.extensions import db

issues_bp = Blueprint('issues', __name__)

@issues_bp.route('/', methods=['GET'])
@jwt_required()
def get_issues():
    issues = Issue.query.all()
    return jsonify([{
        'listing_id': i.listing_id,
        'sku': i.sku,
        'priority': i.priority,
        'marketplace': i.marketplace,
        'description': i.description,
        'status': i.status,
        'link_url': i.link_url,
        'assigned_to': i.assigned_to,
        'created_at': i.created_at.isoformat() if i.created_at else None,
        'updated_at': i.updated_at.isoformat() if i.updated_at else None
    } for i in issues]), 200

@issues_bp.route('/', methods=['POST'])
@jwt_required()
def create_issue():
    data = request.get_json()
    issue = Issue(
        listing_id=data['listing_id'],
        sku=data['sku'],
        priority=data.get('priority', 'high'),
        marketplace=data['marketplace'],
        description=data['description'],
        status=data.get('status', 'open'),
        link_url=data.get('link_url'),
        assigned_to=data.get('assigned_to')
    )
    db.session.add(issue)
    db.session.commit()
    return jsonify({"message": "Issue created"}), 201