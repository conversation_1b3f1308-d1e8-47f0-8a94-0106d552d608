
## Overview

The **Listing Issue Tracker Dashboard** allows teams to track, manage, and resolve listing-related issues across platforms. It provides real-time updates, status filters, Slack and email notifications, and user role management (Admin & Regular Users). 


## Setup Instructions

1. Fork the Repository:
   - Go to the repository on GitHub.
   - Click the **"Fork"** button at the top-right corner to create a copy under your own GitHub account.

2. Clone Your Forked Repository:
   ```sh
   git clone https://github.com/YourUsername/listing-fix-tracker.git
   cd listing-fix-tracker

3. Create and Activate a Virtual Environment:
   ```sh
   python -m venv .venv
   source .venv/bin/activate        # On Windows use `.venv\Scripts\activate`

4. Install Dependencies:
   ```sh
   pip install -r requirements.txt

## Integrated Features
  The Listing Issue Tracker supports:

  - Listing Issue Management (Open, In Progress, Resolved)
  - Admin/User roles with authentication
  - Slack notifications for real-time issue updates
  - Email alerts on issue creation/resolution
  - Issue filtering and dashboard statistics

## Usage
  Run the Flask development server:
  ```sh
  python run.py
