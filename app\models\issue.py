from datetime import datetime, timezone
from app.extensions import db

class Issue(db.Model):
    listing_id = db.Column(db.Integer, primary_key=True)
    sku = db.Column(db.String(10))
    priority = db.Column(db.String(20), default='high')
    marketplace = db.Column(db.String(20))
    description = db.Column(db.Text)
    status = db.Column(db.String(20), default='open')
    link_url = db.Column(db.Text)
    assigned_to = db.Column(db.Inte<PERSON>, db.<PERSON>ey('user.id'), nullable=True)
    created_at = db.Column(db.DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))